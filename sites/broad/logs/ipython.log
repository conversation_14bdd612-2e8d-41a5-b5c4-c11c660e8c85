2025-09-15 22:11:20,084 INFO ipython === bench console session ===
2025-09-15 22:11:20,084 INFO ipython # Test the API methods
2025-09-15 22:11:20,085 INFO ipython import frappe
2025-09-15 22:11:20,085 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:11:20,085 INFO ipython # Test get_scheduled_broadcasts
2025-09-15 22:11:20,085 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:11:20,085 INFO ipython print("get_scheduled_broadcasts result:", result)
2025-09-15 22:11:20,085 INFO ipython # Test sync_detection_system
2025-09-15 22:11:20,085 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:11:20,085 INFO ipython print("sync_detection_system result:", sync_result)
2025-09-15 22:11:20,085 INFO ipython # Test task functions
2025-09-15 22:11:20,086 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:11:20,086 INFO ipython # Test mark_missed_advertisements
2025-09-15 22:11:20,086 INFO ipython mark_missed_advertisements()
2025-09-15 22:11:20,086 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:11:20,086 INFO ipython # Test generate_daily_report
2025-09-15 22:11:20,086 INFO ipython generate_daily_report()
2025-09-15 22:11:20,086 INFO ipython print("generate_daily_report completed")
2025-09-15 22:11:20,086 INFO ipython === session end ===
2025-09-15 22:14:15,682 INFO ipython === bench console session ===
2025-09-15 22:14:15,682 INFO ipython # Quick test of the broadcast app
2025-09-15 22:14:15,682 INFO ipython import frappe
2025-09-15 22:14:15,682 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:14:15,682 INFO ipython # Test API methods
2025-09-15 22:14:15,682 INFO ipython print("Testing get_scheduled_broadcasts...")
2025-09-15 22:14:15,682 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:14:15,682 INFO ipython print("Result:", result)
2025-09-15 22:14:15,683 INFO ipython print("\nTesting sync_detection_system...")
2025-09-15 22:14:15,683 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:14:15,683 INFO ipython print("Sync result:", sync_result)
2025-09-15 22:14:15,683 INFO ipython # Check app statistics
2025-09-15 22:14:15,683 INFO ipython print("\nApp Statistics:")
2025-09-15 22:14:15,683 INFO ipython total_ads = frappe.db.count("Advertisement Broadcast")
2025-09-15 22:14:15,683 INFO ipython print(f"Total Advertisements: {total_ads}")
2025-09-15 22:14:15,683 INFO ipython # Test task functions
2025-09-15 22:14:15,683 INFO ipython print("\nTesting scheduled tasks...")
2025-09-15 22:14:15,683 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:14:15,684 INFO ipython mark_missed_advertisements()
2025-09-15 22:14:15,684 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:14:15,684 INFO ipython generate_daily_report()
2025-09-15 22:14:15,684 INFO ipython print("generate_daily_report completed")
2025-09-15 22:14:15,684 INFO ipython print("\n✅ All tests passed! Broadcast app is working perfectly!")
2025-09-15 22:14:15,684 INFO ipython === session end ===
2025-09-15 23:12:57,340 INFO ipython === bench console session ===
2025-09-15 23:12:57,340 INFO ipython exec(open('test_broadcast_fixes.py').read())
2025-09-15 23:12:57,341 INFO ipython # Test the API methods
2025-09-15 23:12:57,341 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, manual_broadcast_log
2025-09-15 23:12:57,341 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 23:12:57,341 INFO ipython print("API Test:", result['status'])
2025-09-15 23:12:57,341 INFO ipython # Test creating advertisement broadcast
2025-09-15 23:12:57,341 INFO ipython from frappe.utils import now_datetime, add_to_date
2025-09-15 23:12:57,341 INFO ipython test_ad = frappe.get_doc({
    "doctype": "Advertisement Broadcast",
        "advertisement_title": "Test Advertisement - Functionality Check",
            "customer": "Test Customer",
                "presenter": "<EMAIL>",
                    "scheduled_date": add_to_date(now_datetime(), days=1).date(),
                        "scheduled_time": "10:00:00",
                            "duration_seconds": 30,
                                "rate_per_second": 10.0,
                                    "status": "Scheduled",
                                        "auto_generate_invoice": 1,
                                            "notification_sent": 0
                                            })
2025-09-15 23:12:57,342 INFO ipython test_ad.insert(ignore_permissions=True)
2025-09-15 23:12:57,342 INFO ipython print("Advertisement created:", test_ad.name)
2025-09-15 23:12:57,342 INFO ipython print("auto_generate_invoice:", test_ad.auto_generate_invoice)
2025-09-15 23:12:57,342 INFO ipython print("notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test notification functionality
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_not_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_not_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test manual broadcast log API
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython === session end ===
2025-09-15 23:18:53,979 INFO ipython === bench console session ===
2025-09-15 23:18:53,980 INFO ipython # Test the service item creation
2025-09-15 23:18:53,980 INFO ipython from broadcast.broadcast.doctype.advertisement_broadcast.advertisement_broadcast import AdvertisementBroadcast
2025-09-15 23:18:53,980 INFO ipython test_doc = AdvertisementBroadcast()
2025-09-15 23:18:53,980 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,980 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,980 INFO ipython # Test the service item creation correctly
2025-09-15 23:18:53,980 INFO ipython test_doc = frappe.get_doc("Advertisement Broadcast")
2025-09-15 23:18:53,981 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,981 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,981 INFO ipython # Test the service item creation with new doc
2025-09-15 23:18:53,981 INFO ipython test_doc = frappe.new_doc("Advertisement Broadcast")
2025-09-15 23:18:53,981 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,981 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,981 INFO ipython === session end ===
