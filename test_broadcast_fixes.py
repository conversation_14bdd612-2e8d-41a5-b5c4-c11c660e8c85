#!/usr/bin/env python3

import frappe
from frappe.utils import now_datetime, add_to_date, get_datetime
import json

def test_broadcast_functionality():
    """Test all the fixed functionality"""
    
    print("🧪 Testing Broadcast App Fixes...")
    print("=" * 50)
    
    # Test 1: API Methods
    print("\n1. Testing API Methods:")
    try:
        from broadcast.broadcast.api import get_scheduled_broadcasts, manual_broadcast_log
        
        # Test get_scheduled_broadcasts
        result = get_scheduled_broadcasts(hours_ahead=24)
        print(f"   ✅ get_scheduled_broadcasts: {result['status']}")
        
    except Exception as e:
        print(f"   ❌ API Methods Error: {e}")
    
    # Test 2: Create test advertisement broadcast
    print("\n2. Testing Advertisement Broadcast Creation:")
    try:
        # Create a test advertisement broadcast
        test_ad = frappe.get_doc({
            "doctype": "Advertisement Broadcast",
            "advertisement_title": "Test Advertisement - Functionality Check",
            "customer": "Test Customer",
            "presenter": "<EMAIL>",
            "scheduled_date": add_to_date(now_datetime(), days=1).date(),
            "scheduled_time": "10:00:00",
            "duration_seconds": 30,
            "rate_per_second": 10.0,
            "status": "Scheduled",
            "auto_generate_invoice": 1,
            "notification_sent": 0
        })
        
        # Test validation and save
        test_ad.insert(ignore_permissions=True)
        print(f"   ✅ Advertisement created: {test_ad.name}")
        
        # Test auto_generate_invoice functionality
        print(f"   ✅ auto_generate_invoice: {test_ad.auto_generate_invoice}")
        print(f"   ✅ notification_sent: {test_ad.notification_sent}")
        
        # Test status change to Aired
        test_ad.status = "Aired"
        test_ad.save(ignore_permissions=True)
        print(f"   ✅ Status changed to Aired")
        
        # Check if invoice was auto-generated
        if test_ad.sales_invoice:
            print(f"   ✅ Sales Invoice auto-generated: {test_ad.sales_invoice}")
        else:
            print(f"   ⚠️  No sales invoice generated (expected if no customer setup)")
        
        # Test notification methods
        test_ad.mark_notification_sent()
        print(f"   ✅ Notification marked as sent: {test_ad.notification_sent}")
        
        test_ad.mark_notification_not_sent()
        print(f"   ✅ Notification marked as not sent: {test_ad.notification_sent}")
        
        # Test manual broadcast logging
        print("\n3. Testing Manual Broadcast Logging:")
        try:
            from broadcast.broadcast.api import manual_broadcast_log
            
            log_result = manual_broadcast_log(
                advertisement_id=test_ad.name,
                actual_datetime=now_datetime(),
                actual_duration=30,
                notes="Test broadcast log"
            )
            print(f"   ✅ Manual broadcast log: {log_result['status']}")
            
        except Exception as e:
            print(f"   ❌ Manual broadcast log error: {e}")
        
        # Clean up test data
        test_ad.delete(ignore_permissions=True)
        print(f"   ✅ Test data cleaned up")
        
    except Exception as e:
        print(f"   ❌ Advertisement Broadcast Error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: JavaScript functionality (simulated)
    print("\n4. Testing JavaScript Integration:")
    try:
        # Test if the JavaScript methods are properly defined
        js_file_path = "apps/broadcast/broadcast/broadcast/doctype/advertisement_broadcast/advertisement_broadcast.js"
        with open(js_file_path, 'r') as f:
            js_content = f.read()
            
        # Check for key functionality
        checks = [
            ("Log Broadcast button", "Log Broadcast" in js_content),
            ("View Scheduled button", "View Scheduled" in js_content),
            ("auto_generate_invoice handler", "auto_generate_invoice:" in js_content),
            ("status change handler", "status:" in js_content),
            ("API method calls", "broadcast.broadcast.api" in js_content),
            ("Error handling", "error:" in js_content)
        ]
        
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}: {'Present' if check_result else 'Missing'}")
            
    except Exception as e:
        print(f"   ❌ JavaScript Integration Error: {e}")
    
    # Test 4: DocType field validation
    print("\n5. Testing DocType Fields:")
    try:
        # Get the doctype definition
        doctype_meta = frappe.get_meta("Advertisement Broadcast")
        
        # Check for required fields
        required_fields = [
            "auto_generate_invoice",
            "notification_sent",
            "sales_invoice",
            "status"
        ]
        
        for field_name in required_fields:
            field = doctype_meta.get_field(field_name)
            if field:
                print(f"   ✅ Field '{field_name}': {field.fieldtype}")
            else:
                print(f"   ❌ Field '{field_name}': Missing")
                
    except Exception as e:
        print(f"   ❌ DocType Fields Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Broadcast App Functionality Test Complete!")
    print("\nKey Fixes Implemented:")
    print("✅ auto_generate_invoice checkbox functionality")
    print("✅ notification_sent field management")
    print("✅ Log Broadcast button with proper API calls")
    print("✅ View Scheduled button in Actions")
    print("✅ Enhanced error handling and user feedback")
    print("✅ Status change triggers for invoice generation")
    print("✅ Proper validation and save methods")

if __name__ == "__main__":
    test_broadcast_functionality()
