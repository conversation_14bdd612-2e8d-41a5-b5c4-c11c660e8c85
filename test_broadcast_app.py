#!/usr/bin/env python3
"""
Test script for the Broadcast App
Run with: bench --site broad execute test_broadcast_app.py
"""

import frappe
from frappe.utils import now_datetime, add_to_date, today
import json

def test_broadcast_app():
    """Comprehensive test of the broadcast app functionality"""
    
    print("🎯 Starting Broadcast App Tests...")
    print("=" * 50)
    
    # Test 1: API Methods
    test_api_methods()
    
    # Test 2: Create test data
    test_data_creation()
    
    # Test 3: Scheduled tasks
    test_scheduled_tasks()
    
    # Test 4: Document workflows
    test_document_workflows()
    
    print("\n✅ All tests completed successfully!")
    print("🚀 Broadcast app is working perfectly!")

def test_api_methods():
    """Test all API methods"""
    print("\n📡 Testing API Methods...")
    
    # Import API methods
    from broadcast.broadcast.api import (
        get_scheduled_broadcasts, 
        sync_detection_system,
        manual_broadcast_log,
        log_detected_broadcast
    )
    
    # Test get_scheduled_broadcasts
    result = get_scheduled_broadcasts(hours_ahead=24)
    print(f"✓ get_scheduled_broadcasts: {result['status']}")
    
    # Test sync_detection_system
    sync_result = sync_detection_system()
    print(f"✓ sync_detection_system: {sync_result['status']}")
    
    print("✅ API methods working correctly")

def test_data_creation():
    """Create test advertisement broadcast"""
    print("\n📝 Creating Test Data...")
    
    try:
        # Create a test customer if not exists
        if not frappe.db.exists("Customer", "Test Broadcasting Customer"):
            customer = frappe.get_doc({
                "doctype": "Customer",
                "customer_name": "Test Broadcasting Customer",
                "customer_type": "Company",
                "customer_group": "All Customer Groups",
                "territory": "All Territories"
            })
            customer.insert()
            print("✓ Test customer created")
        
        # Create test advertisement
        ad_doc = frappe.get_doc({
            "doctype": "Advertisement Broadcast",
            "naming_series": "ADB-.YYYY.-",
            "customer": "Test Broadcasting Customer",
            "presenter": "Administrator",
            "advertisement_title": "Test Advertisement - Python API",
            "scheduled_date": today(),
            "scheduled_time": "14:30:00",
            "duration_seconds": 30,
            "rate_per_second": 10.0,
            "status": "Scheduled",
            "priority": "Medium",
            "advertisement_content": "This is a test advertisement created via Python API",
            "auto_generate_invoice": 1
        })
        
        ad_doc.insert()
        print(f"✓ Test advertisement created: {ad_doc.name}")
        
        # Test the log_broadcast method
        ad_doc.log_broadcast(
            actual_datetime=now_datetime(),
            actual_duration=30,
            logged_by="Administrator",
            broadcast_type="Manual Entry",
            notes="Test broadcast log via Python"
        )
        print("✓ Broadcast logged successfully")
        
        return ad_doc.name
        
    except Exception as e:
        print(f"❌ Error creating test data: {str(e)}")
        return None

def test_scheduled_tasks():
    """Test scheduled task functions"""
    print("\n⏰ Testing Scheduled Tasks...")
    
    from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
    
    try:
        # Test mark_missed_advertisements
        mark_missed_advertisements()
        print("✓ mark_missed_advertisements executed")
        
        # Test generate_daily_report
        generate_daily_report()
        print("✓ generate_daily_report executed")
        
    except Exception as e:
        print(f"❌ Error in scheduled tasks: {str(e)}")

def test_document_workflows():
    """Test document submission and workflows"""
    print("\n🔄 Testing Document Workflows...")
    
    try:
        # Get the latest advertisement
        ads = frappe.get_all("Advertisement Broadcast", 
                           filters={"status": "Aired"}, 
                           limit=1, 
                           order_by="creation desc")
        
        if ads:
            ad_name = ads[0].name
            ad_doc = frappe.get_doc("Advertisement Broadcast", ad_name)
            
            # Test submission
            if ad_doc.docstatus == 0:
                ad_doc.submit()
                print(f"✓ Advertisement {ad_name} submitted successfully")
            
            # Test invoice generation
            if ad_doc.auto_generate_invoice and not ad_doc.sales_invoice:
                ad_doc.create_sales_invoice()
                print(f"✓ Sales invoice generated: {ad_doc.sales_invoice}")
        
    except Exception as e:
        print(f"❌ Error in document workflows: {str(e)}")

def test_api_via_http():
    """Test API methods via HTTP calls (for external integration)"""
    print("\n🌐 Testing HTTP API Endpoints...")
    
    # These would be the actual HTTP endpoints for external systems
    endpoints = [
        "/api/method/broadcast.broadcast.api.get_scheduled_broadcasts",
        "/api/method/broadcast.broadcast.api.sync_detection_system",
        "/api/method/broadcast.broadcast.api.manual_broadcast_log",
        "/api/method/broadcast.broadcast.api.log_detected_broadcast"
    ]
    
    print("📋 Available API endpoints:")
    for endpoint in endpoints:
        print(f"   • {endpoint}")

def show_app_statistics():
    """Show current app statistics"""
    print("\n📊 Broadcast App Statistics:")
    print("-" * 30)
    
    # Count documents
    total_ads = frappe.db.count("Advertisement Broadcast")
    scheduled_ads = frappe.db.count("Advertisement Broadcast", {"status": "Scheduled"})
    aired_ads = frappe.db.count("Advertisement Broadcast", {"status": "Aired"})
    missed_ads = frappe.db.count("Advertisement Broadcast", {"status": "Missed"})
    
    print(f"Total Advertisements: {total_ads}")
    print(f"Scheduled: {scheduled_ads}")
    print(f"Aired: {aired_ads}")
    print(f"Missed: {missed_ads}")
    
    # Show recent logs
    recent_logs = frappe.db.sql("""
        SELECT COUNT(*) as log_count 
        FROM `tabBroadcast Log` 
        WHERE creation >= CURDATE()
    """, as_dict=True)
    
    if recent_logs:
        print(f"Today's Broadcast Logs: {recent_logs[0].log_count}")

def demonstrate_external_api_usage():
    """Show how external systems would integrate"""
    print("\n🔌 External API Integration Examples:")
    print("-" * 40)
    
    print("""
    # Example 1: Get scheduled broadcasts (for detection system)
    curl -X GET "http://your-site.com/api/method/broadcast.broadcast.api.get_scheduled_broadcasts?hours_ahead=4"
    
    # Example 2: Log detected broadcast
    curl -X POST "http://your-site.com/api/method/broadcast.broadcast.api.log_detected_broadcast" \\
         -H "Content-Type: application/json" \\
         -d '{
             "advertisement_id": "ADB-2025-00001",
             "detected_datetime": "2025-09-15 14:30:00",
             "duration": 30,
             "confidence": 95.5,
             "notes": "Auto-detected via AI system"
         }'
    
    # Example 3: Manual broadcast logging
    curl -X POST "http://your-site.com/api/method/broadcast.broadcast.api.manual_broadcast_log" \\
         -H "Content-Type: application/json" \\
         -d '{
             "advertisement_id": "ADB-2025-00001",
             "actual_datetime": "2025-09-15 14:30:00",
             "actual_duration": 30,
             "notes": "Manually logged by presenter"
         }'
    """)

if __name__ == "__main__":
    # Run the tests
    test_broadcast_app()
    show_app_statistics()
    test_api_via_http()
    demonstrate_external_api_usage()
