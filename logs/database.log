2025-09-12 18:10:26,271 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`item_name` varchar(140),
`bom_detail_no` varchar(140),
`description` longtext,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`available_qty_for_consumption` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`current_stock` decimal(21,9) not null default 0,
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`subcontracting_order` varchar(140),
`serial_no` text,
`batch_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:28,715 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`is_scrap_item` int(1) not null default 0,
`description` longtext,
`brand` varchar(140),
`image` text,
`received_qty` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`rejected_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`rm_cost_per_qty` decimal(21,9) not null default 0,
`service_cost_per_qty` decimal(21,9) not null default 0,
`additional_cost_per_qty` decimal(21,9) not null default 0,
`scrap_cost_per_qty` decimal(21,9) not null default 0,
`rm_supp_cost` decimal(21,9) not null default 0,
`warehouse` varchar(140),
`subcontracting_order` varchar(140),
`subcontracting_order_item` varchar(140),
`subcontracting_receipt_item` varchar(140),
`rejected_warehouse` varchar(140),
`bom` varchar(140),
`include_exploded_items` int(1) not null default 0,
`quality_inspection` varchar(140),
`schedule_date` date,
`reference_name` varchar(140),
`serial_and_batch_bundle` varchar(140),
`use_serial_batch_fields` int(1) not null default 0,
`rejected_serial_and_batch_bundle` varchar(140),
`serial_no` text,
`rejected_serial_no` text,
`batch_no` varchar(140),
`manufacturer` varchar(140),
`manufacturer_part_no` varchar(140),
`expense_account` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`page_break` int(1) not null default 0,
`purchase_order` varchar(140),
`purchase_order_item` varchar(140),
index `item_code`(`item_code`),
index `subcontracting_order`(`subcontracting_order`),
index `subcontracting_order_item`(`subcontracting_order_item`),
index `purchase_order`(`purchase_order`),
index `purchase_order_item`(`purchase_order_item`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:29,473 WARNING database DDL Query made to DB:
create table `tabSubcontracting Receipt` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_delivery_note` varchar(140),
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`set_posting_time` int(1) not null default 0,
`is_return` int(1) not null default 0,
`return_against` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`rejected_warehouse` varchar(140),
`supplier_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`in_words` varchar(240),
`bill_no` varchar(140),
`bill_date` date,
`supplier_address` varchar(140),
`contact_person` varchar(140),
`address_display` text,
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`range` varchar(140),
`represents_company` varchar(140),
`status` varchar(140) default 'Draft',
`per_returned` decimal(21,9) not null default 0,
`auto_repeat` varchar(140),
`letter_head` varchar(140),
`language` varchar(140),
`instructions` text,
`select_print_heading` varchar(140),
`remarks` text,
`transporter_name` varchar(140),
`lr_no` varchar(140),
`lr_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `posting_date`(`posting_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:29,603 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order Supplied Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`main_item_code` varchar(140),
`rm_item_code` varchar(140),
`stock_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 1.0,
`reserve_warehouse` varchar(140),
`bom_detail_no` varchar(140),
`reference_name` varchar(140),
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`required_qty` decimal(21,9) not null default 0,
`supplied_qty` decimal(21,9) not null default 0,
`consumed_qty` decimal(21,9) not null default 0,
`returned_qty` decimal(21,9) not null default 0,
`total_supplied_qty` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:29,736 WARNING database DDL Query made to DB:
create table `tabSubcontracting BOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_active` int(1) not null default 1,
`finished_good` varchar(140),
`finished_good_qty` decimal(21,9) not null default 1.0,
`finished_good_uom` varchar(140),
`finished_good_bom` varchar(140),
`service_item` varchar(140),
`service_item_qty` decimal(21,9) not null default 1.0,
`service_item_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `finished_good`(`finished_good`),
index `finished_good_bom`(`finished_good_bom`),
index `service_item`(`service_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:29,916 WARNING database DDL Query made to DB:
create table `tabSubcontracting Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) default '{supplier_name}',
`naming_series` varchar(140),
`purchase_order` varchar(140),
`supplier` varchar(140),
`supplier_name` varchar(140),
`supplier_warehouse` varchar(140),
`company` varchar(140),
`transaction_date` date,
`schedule_date` date,
`amended_from` varchar(140),
`cost_center` varchar(140),
`project` varchar(140),
`set_warehouse` varchar(140),
`total_qty` decimal(21,9) not null default 0,
`total` decimal(21,9) not null default 0,
`set_reserve_warehouse` varchar(140),
`supplier_address` varchar(140),
`address_display` text,
`contact_person` varchar(140),
`contact_display` text,
`contact_mobile` text,
`contact_email` text,
`shipping_address` varchar(140),
`shipping_address_display` text,
`billing_address` varchar(140),
`billing_address_display` text,
`distribute_additional_costs_based_on` varchar(140) default 'Qty',
`total_additional_costs` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`per_received` decimal(21,9) not null default 0,
`select_print_heading` varchar(140),
`letter_head` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `supplier`(`supplier`),
index `transaction_date`(`transaction_date`),
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:30,022 WARNING database DDL Query made to DB:
create table `tabCommon Code` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_list` varchar(140),
`title` varchar(300),
`common_code` varchar(300),
`description` text,
`additional_data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_list`(`code_list`),
index `common_code`(`common_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:30,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommon Code`
				ADD INDEX IF NOT EXISTS `code_list_common_code_index`(code_list, common_code)
2025-09-12 18:10:30,147 WARNING database DDL Query made to DB:
create table `tabCode List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`canonical_uri` varchar(140),
`url` varchar(140),
`default_common_code` varchar(140),
`version` varchar(140),
`publisher` varchar(140),
`publisher_id` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-12 18:10:39,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabAddress` ADD COLUMN `tax_category` varchar(140), ADD COLUMN `is_your_company_address` int(1) not null default 0
2025-09-12 18:10:39,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact` ADD COLUMN `is_billing_contact` int(1) not null default 0
2025-09-12 18:22:21,159 WARNING database DDL Query made to DB:
create table `tabBroadcast Message` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-13 13:31:16,911 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-13 13:31:18,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-13 13:31:19,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-13 13:41:23,295 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-13 13:41:24,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-13 13:41:25,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-13 13:41:27,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-13 13:41:28,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-13 13:41:29,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-13 13:41:30,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-13 13:41:34,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-09-14 16:16:54,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabBroadcast Message` ADD COLUMN `_user_tags` text, ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-09-14 16:18:01,273 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-14 16:18:02,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-14 16:18:03,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-14 16:19:57,518 WARNING database DDL Query made to DB:
create table `tabAdvertisement Broadcast` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-14 17:57:50,488 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-14 17:57:51,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-14 17:57:52,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-14 17:57:53,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvertisement Broadcast` ADD COLUMN `naming_series` varchar(140), ADD COLUMN `customer` varchar(140), ADD COLUMN `presenter` varchar(140), ADD COLUMN `advertisement_title` varchar(140), ADD COLUMN `scheduled_date` date, ADD COLUMN `scheduled_time` time(6), ADD COLUMN `duration_seconds` int(11) not null default 0, ADD COLUMN `rate_per_second` decimal(21,9) not null default 0, ADD COLUMN `total_amount` decimal(21,9) not null default 0, ADD COLUMN `status` varchar(140) default 'Scheduled', ADD COLUMN `priority` varchar(140) default 'Medium', ADD COLUMN `advertisement_content` longtext, ADD COLUMN `sales_invoice` varchar(140), ADD COLUMN `auto_generate_invoice` int(1) not null default 1, ADD COLUMN `notification_sent` int(1) not null default 0, ADD COLUMN `_seen` text
2025-09-14 17:58:28,134 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-14 17:58:28,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-14 17:58:29,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-14 17:58:30,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvertisement Broadcast` MODIFY `rate_per_second` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-09-14 18:02:34,997 WARNING database DDL Query made to DB:
create table `tabBroadcast Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-09-14 18:04:13,639 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-14 18:04:15,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-14 18:04:17,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-14 18:04:18,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabBroadcast Log` ADD COLUMN `actual_broadcast_datetime` datetime(6), ADD COLUMN `actual_duration` int(11) not null default 0, ADD COLUMN `broadcast_type` varchar(140) default 'Manual Entry', ADD COLUMN `detection_confidence` decimal(21,9) not null default 0, ADD COLUMN `logged_by` varchar(140), ADD COLUMN `notes` text, ADD COLUMN `variance_seconds` int(11) not null default 0
2025-09-14 18:06:29,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvertisement Broadcast` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `rate_per_second` decimal(21,9) not null default 0
2025-09-14 18:07:15,283 WARNING database DDL Query made to DB:
alter table `tabBroadcast Log` add column if not exists parent varchar(140)
2025-09-14 18:07:15,284 WARNING database DDL Query made to DB:
alter table `tabBroadcast Log` add column if not exists parenttype varchar(140)
2025-09-14 18:07:15,285 WARNING database DDL Query made to DB:
alter table `tabBroadcast Log` add column if not exists parentfield varchar(140)
2025-09-14 18:07:15,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabBroadcast Log` MODIFY `detection_confidence` decimal(21,9) not null default 0
2025-09-14 18:26:34,573 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-14 18:26:35,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-14 18:26:36,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-14 18:26:37,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvertisement Broadcast` MODIFY `rate_per_second` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-09-14 18:26:37,377 WARNING database DDL Query made to DB:
ALTER TABLE `tabBroadcast Log` MODIFY `detection_confidence` decimal(21,9) not null default 0
2025-09-15 15:38:29,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `invoice_and_debit` varchar(140)
2025-09-15 15:38:30,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-09-15 15:40:35,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-09-15 17:02:53,323 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-09-15 17:02:57,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-09-15 17:05:38,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-09-15 22:10:57,478 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-15 22:10:58,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-15 22:10:59,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
