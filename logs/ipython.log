2025-08-14 12:49:38,785 INFO ipython optimized_css = """/* ===================== GLOBAL PRINT STYLES ===================== */
.print-format .letter-head {
    display: none;
    }
    
    /* Reset default spacing and alignment */
    .print-format {
        padding: 15px !important;
            font-family: 'Calibri', Arial, sans-serif;
                font-size: 11px;
                    line-height: 1.3;
                        color: #000;
                            max-width: 100%;
                            }
                            
                            .print-format td, .print-format th {
                                vertical-align: top !important;
                                    padding: 3px 5px !important;
                                    }
                                    
                                    .print-format th {
                                        color: black !important;
                                            font-weight: bold;
                                                border-bottom-width: 1px !important;
                                                }
                                                
                                                .print-format p {
                                                    margin: 0px 0px 4px !important;
                                                    }
                                                    
                                                    /* Remove default ERPNext spacing */
                                                    .frappe-control, .form-section {
                                                        margin-bottom: 0 !important;
                                                        }
                                                        
                                                        /* ===================== RESPONSIVE LAYOUT ===================== */
                                                        html, body {
                                                            height: auto !important;
                                                                overflow: visible !important;
                                                                }
                                                                
                                                                .print-format {
                                                                    display: block;
                                                                        height: auto;
                                                                            overflow: visible;
                                                                            }
                                                                            
                                                                            /* ===================== PAGE BREAK HANDLING ===================== */
                                                                            @media print {
                                                                                @page {
                                                                                        margin: 8mm 6mm;
                                                                                                size: A4;
                                                                                                    }
                                                                                                        
                                                                                                            .print-format {
                                                                                                                    padding: 6px !important;
                                                                                                                            font-size: 10px;
                                                                                                                                }
                                                                                                                                    
                                                                                                                                        /* Prevent page breaks inside important sections */
                                                                                                                                            .header-section, .customer-details, .supplier-details, .order-details {
                                                                                                                                                    page-break-inside: avoid;
                                                                                                                                                        }
                                                                                                                                                            
                                                                                                                                                                /* Allow page breaks between item rows if needed */
                                                                                                                                                                    .items-table tbody tr {
                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                    page-break-after: auto;
                                                                                                                                                                                        }
                                                                                                                                                                                            
                                                                                                                                                                                                /* Keep footer sections together */
                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                                                    margin-top: 10px;
                                                                                                                                                                                                                        }
                                                                                                                                                                                                                            
                                                                                                                                                                                                                                /* Optimize spacing for print */
                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                            margin-top: 8px !important;
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                /* ===================== DYNAMIC CONTENT HANDLING ===================== */
                                                                                                                                                                                                                                                .content-wrapper {
                                                                                                                                                                                                                                                    display: flex;
                                                                                                                                                                                                                                                        flex-direction: column;
                                                                                                                                                                                                                                                            min-height: auto;
                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                            .items-section {
                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                    margin: 12px 0;
                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                                                                                        margin-top: auto;
                                                                                                                                                                                                                                                                            padding-top: 12px;
                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== ALIGNMENT FIXES ===================== */
                                                                                                                                                                                                                                                                            .text-left { text-align: left !important; }
                                                                                                                                                                                                                                                                            .text-center { text-align: center !important; }
                                                                                                                                                                                                                                                                            .text-right { text-align: right !important; }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== TABLE IMPROVEMENTS ===================== */
                                                                                                                                                                                                                                                                            .items-table {
                                                                                                                                                                                                                                                                                width: 100%;
                                                                                                                                                                                                                                                                                    border-collapse: collapse;
                                                                                                                                                                                                                                                                                        margin: 12px 0;
                                                                                                                                                                                                                                                                                            font-size: 11px;
                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                            .items-table th {
                                                                                                                                                                                                                                                                                                border-top: 1px solid #000;
                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #000;
                                                                                                                                                                                                                                                                                                        padding: 4px 6px;
                                                                                                                                                                                                                                                                                                            font-weight: bold;
                                                                                                                                                                                                                                                                                                                background-color: #f8f9fa;
                                                                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                                                                                .items-table td {
                                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #ddd;
                                                                                                                                                                                                                                                                                                                        padding: 3px 6px;
                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                        /* Handle many items by reducing row height */
                                                                                                                                                                                                                                                                                                                        .items-table.many-items {
                                                                                                                                                                                                                                                                                                                            font-size: 10px;
                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                            .items-table.many-items td,
                                                                                                                                                                                                                                                                                                                            .items-table.many-items th {
                                                                                                                                                                                                                                                                                                                                padding: 2px 4px;
                                                                                                                                                                                                                                                                                                                                    line-height: 1.1;
                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                    /* ===================== RESPONSIVE SPACING ===================== */
                                                                                                                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                                                                                                                        margin-top: 12px;
                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                        @media print {
                                                                                                                                                                                                                                                                                                                                            .responsive-margin {
                                                                                                                                                                                                                                                                                                                                                    margin-top: 6px;
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                                        /* ===================== FOOTER LAYOUT ===================== */
                                                                                                                                                                                                                                                                                                                                                        .footer-layout {
                                                                                                                                                                                                                                                                                                                                                            display: flex;
                                                                                                                                                                                                                                                                                                                                                                justify-content: space-between;
                                                                                                                                                                                                                                                                                                                                                                    align-items: flex-start;
                                                                                                                                                                                                                                                                                                                                                                        margin-top: 15px;
                                                                                                                                                                                                                                                                                                                                                                            gap: 20px;
                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                            .footer-left {
                                                                                                                                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                    max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                    .footer-right {
                                                                                                                                                                                                                                                                                                                                                                                        flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                            max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                                            @media print {
                                                                                                                                                                                                                                                                                                                                                                                                .footer-layout {
                                                                                                                                                                                                                                                                                                                                                                                                        margin-top: 8px;
                                                                                                                                                                                                                                                                                                                                                                                                                gap: 10px;
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                                                    """
2025-08-14 12:49:38,791 INFO ipython print("CSS defined successfully!")
2025-08-14 12:49:38,791 INFO ipython # Update Rubis Invoice
2025-08-14 12:49:38,791 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Invoice")
2025-08-14 12:49:38,792 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,792 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,792 INFO ipython doc.save()
2025-08-14 12:49:38,792 INFO ipython print("✅ Rubis Invoice updated!")
2025-08-14 12:49:38,792 INFO ipython # Update Rubis Sales Order
2025-08-14 12:49:38,793 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Sales Order")
2025-08-14 12:49:38,793 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,793 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,793 INFO ipython doc.save()
2025-08-14 12:49:38,793 INFO ipython print("✅ Rubis Sales Order updated!")
2025-08-14 12:49:38,794 INFO ipython # Update Rubis Purchase Order
2025-08-14 12:49:38,794 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Order")
2025-08-14 12:49:38,794 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,794 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,794 INFO ipython doc.save()
2025-08-14 12:49:38,794 INFO ipython print("✅ Rubis Purchase Order updated!")
2025-08-14 12:49:38,795 INFO ipython # Update Rubis Purchase Receipt
2025-08-14 12:49:38,795 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Receipt")
2025-08-14 12:49:38,795 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,795 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,795 INFO ipython doc.save()
2025-08-14 12:49:38,796 INFO ipython print("✅ Rubis Purchase Receipt updated!")
2025-08-14 12:49:38,796 INFO ipython # Commit all changes
2025-08-14 12:49:38,796 INFO ipython frappe.db.commit()
2025-08-14 12:49:38,796 INFO ipython print("🎉 All print formats updated successfully in your rubis site!")
2025-08-14 12:49:38,796 INFO ipython === session end ===
2025-08-18 09:08:00,866 INFO ipython === bench console session ===
2025-08-18 09:08:00,866 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:08:00,867 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:08:00,867 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:08:00,867 INFO ipython from frappe.utils import getdate
2025-08-18 09:08:00,867 INFO ipython print("Available utils:", dir(frappe.utils)[:10])
2025-08-18 09:08:00,867 INFO ipython [x for x in dir(frappe.utils) if 'year' in x.lower()]
2025-08-18 09:08:00,867 INFO ipython === session end ===
2025-08-18 09:14:01,656 INFO ipython === bench console session ===
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,656 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,657 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,657 INFO ipython from datetime import date
2025-08-18 09:14:01,658 INFO ipython import calendar
2025-08-18 09:14:01,658 INFO ipython april_7_2024 = date(2024, 4, 7)
2025-08-18 09:14:01,658 INFO ipython print("April 7, 2024 is a:", calendar.day_name[april_7_2024.weekday()])
2025-08-18 09:14:01,658 INFO ipython === session end ===
2025-08-18 09:24:47,729 INFO ipython === bench console session ===
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:24:47,730 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:24:47,730 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import get_saturday_overtime_hours
2025-08-18 09:24:47,730 INFO ipython from datetime import date
2025-08-18 09:24:47,730 INFO ipython # Test Saturday overtime calculation
2025-08-18 09:24:47,730 INFO ipython saturday_date = date(2024, 4, 6)  # A Saturday
2025-08-18 09:24:47,730 INFO ipython saturday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", saturday_date)
2025-08-18 09:24:47,730 INFO ipython print(f"Saturday overtime for 12:00-17:00: {saturday_ot}")
2025-08-18 09:24:47,731 INFO ipython # Test non-Saturday
2025-08-18 09:24:47,731 INFO ipython monday_date = date(2024, 4, 8)  # A Monday
2025-08-18 09:24:47,731 INFO ipython monday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", monday_date)
2025-08-18 09:24:47,731 INFO ipython print(f"Monday overtime (should be 0): {monday_ot}")
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:24:47,731 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:24:47,731 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import apply_custom_fields
2025-08-18 09:24:47,731 INFO ipython apply_custom_fields()
2025-08-18 09:24:47,732 INFO ipython print("Custom fields applied")
2025-08-18 09:24:47,732 INFO ipython from csf_tz.patches.setup_biometrics_system import execute
2025-08-18 09:24:47,732 INFO ipython execute()
2025-08-18 09:24:47,732 INFO ipython print("Full setup completed")
2025-08-18 09:24:47,732 INFO ipython === session end ===
2025-08-21 10:03:58,991 INFO ipython === bench console session ===
2025-08-21 10:03:58,991 INFO ipython import frappe
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee OT Component", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Education", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee External Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Internal Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython === session end ===
2025-08-28 10:59:42,766 INFO ipython === bench console session ===
2025-08-28 10:59:42,767 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 10:59:42,767 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 10:59:42,767 INFO ipython columns, data = execute(filters)
2025-08-28 10:59:42,768 INFO ipython print("Report executed successfully!")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 10:59:42,768 INFO ipython frappe.db.describe("Sales Order Item")
2025-08-28 10:59:42,768 INFO ipython === session end ===
2025-08-28 11:03:59,882 INFO ipython === bench console session ===
2025-08-28 11:03:59,882 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:03:59,883 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:03:59,883 INFO ipython columns, data = execute(filters)
2025-08-28 11:03:59,883 INFO ipython print("Report executed successfully!")
2025-08-28 11:03:59,883 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:03:59,884 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython === session end ===
2025-08-28 11:06:58,629 INFO ipython === bench console session ===
2025-08-28 11:06:58,629 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:06:58,629 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:06:58,630 INFO ipython columns, data = execute(filters)
2025-08-28 11:06:58,630 INFO ipython print("Report executed successfully!")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:06:58,630 INFO ipython bench --site rubis console
2025-08-28 11:06:58,630 INFO ipython === session end ===
2025-09-04 17:29:08,223 INFO ipython === bench console session ===
2025-09-04 17:29:08,235 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site explore console
2025-09-04 17:29:08,235 INFO ipython frappe.get_meta("Gate Pass").get_field("workflow_state")
2025-09-04 17:29:08,236 INFO ipython frappe.db.get_list("DocType", filters={"name": ["like", "%Gate%"]}, fields=["name"])
2025-09-04 17:29:08,236 INFO ipython === session end ===
2025-09-10 17:44:51,668 INFO ipython === bench console session ===
2025-09-10 17:44:51,669 INFO ipython bench --site explore console --no-startup
2025-09-10 17:44:51,669 INFO ipython === session end ===
2025-09-10 18:04:43,094 INFO ipython === bench console session ===
2025-09-10 18:04:43,422 INFO ipython gate_passes = frappe.get_all("Gate Pass", limit=5)
2025-09-10 18:04:43,423 INFO ipython print(f"Found {len(gate_passes)} gate passes")
2025-09-10 18:04:43,424 INFO ipython from frappe import get_hooks
2025-09-10 18:04:43,424 INFO ipython hooks = get_hooks("scheduler_events")
2025-09-10 18:04:43,425 INFO ipython print("Scheduler events:", hooks)
2025-09-10 18:04:43,425 INFO ipython from icd_tz.icd_tz.doctype.gate_pass.gate_pass import auto_expire_gate_passes
2025-09-10 18:04:43,425 INFO ipython try:
        result = auto_expire_gate_passes()
            print("Auto expire function executed successfully")
2025-09-10 18:04:43,426 INFO ipython     print(f"Result: {result}")
2025-09-10 18:04:43,426 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-09-10 18:04:43,426 INFO ipython try:
        result = auto_expire_gate_passes()
            print("Auto expire function executed successfully")
2025-09-10 18:04:43,427 INFO ipython     print(f"Result: {result}")
2025-09-10 18:04:43,427 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-09-10 18:04:43,428 INFO ipython result = auto_expire_gate_passes()
2025-09-10 18:04:43,428 INFO ipython print("Function executed successfully")
2025-09-10 18:04:43,428 INFO ipython print("Function completed")
2025-09-10 18:04:43,429 INFO ipython === session end ===
2025-09-15 22:11:20,084 INFO ipython === bench console session ===
2025-09-15 22:11:20,084 INFO ipython # Test the API methods
2025-09-15 22:11:20,085 INFO ipython import frappe
2025-09-15 22:11:20,085 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:11:20,085 INFO ipython # Test get_scheduled_broadcasts
2025-09-15 22:11:20,085 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:11:20,085 INFO ipython print("get_scheduled_broadcasts result:", result)
2025-09-15 22:11:20,085 INFO ipython # Test sync_detection_system
2025-09-15 22:11:20,085 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:11:20,085 INFO ipython print("sync_detection_system result:", sync_result)
2025-09-15 22:11:20,085 INFO ipython # Test task functions
2025-09-15 22:11:20,086 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:11:20,086 INFO ipython # Test mark_missed_advertisements
2025-09-15 22:11:20,086 INFO ipython mark_missed_advertisements()
2025-09-15 22:11:20,086 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:11:20,086 INFO ipython # Test generate_daily_report
2025-09-15 22:11:20,086 INFO ipython generate_daily_report()
2025-09-15 22:11:20,086 INFO ipython print("generate_daily_report completed")
2025-09-15 22:11:20,086 INFO ipython === session end ===
2025-09-15 22:14:15,682 INFO ipython === bench console session ===
2025-09-15 22:14:15,682 INFO ipython # Quick test of the broadcast app
2025-09-15 22:14:15,682 INFO ipython import frappe
2025-09-15 22:14:15,682 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:14:15,682 INFO ipython # Test API methods
2025-09-15 22:14:15,682 INFO ipython print("Testing get_scheduled_broadcasts...")
2025-09-15 22:14:15,682 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:14:15,682 INFO ipython print("Result:", result)
2025-09-15 22:14:15,683 INFO ipython print("\nTesting sync_detection_system...")
2025-09-15 22:14:15,683 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:14:15,683 INFO ipython print("Sync result:", sync_result)
2025-09-15 22:14:15,683 INFO ipython # Check app statistics
2025-09-15 22:14:15,683 INFO ipython print("\nApp Statistics:")
2025-09-15 22:14:15,683 INFO ipython total_ads = frappe.db.count("Advertisement Broadcast")
2025-09-15 22:14:15,683 INFO ipython print(f"Total Advertisements: {total_ads}")
2025-09-15 22:14:15,683 INFO ipython # Test task functions
2025-09-15 22:14:15,683 INFO ipython print("\nTesting scheduled tasks...")
2025-09-15 22:14:15,683 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:14:15,684 INFO ipython mark_missed_advertisements()
2025-09-15 22:14:15,684 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:14:15,684 INFO ipython generate_daily_report()
2025-09-15 22:14:15,684 INFO ipython print("generate_daily_report completed")
2025-09-15 22:14:15,684 INFO ipython print("\n✅ All tests passed! Broadcast app is working perfectly!")
2025-09-15 22:14:15,684 INFO ipython === session end ===
2025-09-15 23:12:57,340 INFO ipython === bench console session ===
2025-09-15 23:12:57,340 INFO ipython exec(open('test_broadcast_fixes.py').read())
2025-09-15 23:12:57,341 INFO ipython # Test the API methods
2025-09-15 23:12:57,341 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, manual_broadcast_log
2025-09-15 23:12:57,341 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 23:12:57,341 INFO ipython print("API Test:", result['status'])
2025-09-15 23:12:57,341 INFO ipython # Test creating advertisement broadcast
2025-09-15 23:12:57,341 INFO ipython from frappe.utils import now_datetime, add_to_date
2025-09-15 23:12:57,341 INFO ipython test_ad = frappe.get_doc({
    "doctype": "Advertisement Broadcast",
        "advertisement_title": "Test Advertisement - Functionality Check",
            "customer": "Test Customer",
                "presenter": "<EMAIL>",
                    "scheduled_date": add_to_date(now_datetime(), days=1).date(),
                        "scheduled_time": "10:00:00",
                            "duration_seconds": 30,
                                "rate_per_second": 10.0,
                                    "status": "Scheduled",
                                        "auto_generate_invoice": 1,
                                            "notification_sent": 0
                                            })
2025-09-15 23:12:57,342 INFO ipython test_ad.insert(ignore_permissions=True)
2025-09-15 23:12:57,342 INFO ipython print("Advertisement created:", test_ad.name)
2025-09-15 23:12:57,342 INFO ipython print("auto_generate_invoice:", test_ad.auto_generate_invoice)
2025-09-15 23:12:57,342 INFO ipython print("notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test notification functionality
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_not_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_not_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test manual broadcast log API
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython === session end ===
