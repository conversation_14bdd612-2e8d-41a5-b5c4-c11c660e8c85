# 📋 Advertisement Broadcast Workflow Documentation

## 🔄 **Status Flow & Stage Management**

### **Complete Workflow Stages:**

```
1. SCHEDULED → 2. AIRED → 3. INVOICE GENERATED
     ↓
4. MISSED → 5. RESCHEDULED (back to SCHEDULED)
     ↓
6. CANCELLED (terminal state)
```

---

## 📊 **Status Definitions & Actions**

### **1. SCHEDULED** 🟡
**Description:** Advertisement is planned and scheduled for broadcast
**Characteristics:**
- Initial status when advertisement is created
- Waiting for broadcast time
- Can be edited and modified

**Available Actions:**
- ✅ **Mark as Aired** - Move to next stage
- ✅ **Cancel** - Move to cancelled state
- ✅ **Edit Details** - Modify schedule/content
- ✅ **Log Broadcast** - Manual logging

**Next Possible States:**
- `Aired` (successful broadcast)
- `Missed` (failed to broadcast)
- `Cancelled` (cancelled before broadcast)

---

### **2. AIRED** 🟢
**Description:** Advertisement has been successfully broadcast
**Characteristics:**
- Broadcast completed successfully
- Revenue can be calculated
- Ready for invoicing

**Available Actions:**
- ✅ **Generate Invoice** - Create sales invoice
- ✅ **View Broadcast Logs** - Check broadcast details
- ✅ **Mark as Missed** - If incorrectly marked

**Next Possible States:**
- `Invoice Generated` (automatic after invoice creation)
- `Missed` (if correction needed)

---

### **3. MISSED** 🔴
**Description:** Advertisement failed to broadcast as scheduled
**Characteristics:**
- Broadcast did not occur
- Revenue loss potential
- Requires action (reschedule/cancel)

**Available Actions:**
- ✅ **Reschedule** - Set new date/time, return to Scheduled
- ✅ **Cancel** - Move to cancelled state
- ✅ **Mark as Aired** - If actually broadcast

**Next Possible States:**
- `Scheduled` (after rescheduling)
- `Cancelled` (if cannot reschedule)
- `Aired` (if correction needed)

---

### **4. CANCELLED** ⚫
**Description:** Advertisement has been cancelled
**Characteristics:**
- Terminal state - no further actions
- No revenue expected
- Historical record only

**Available Actions:**
- ✅ **View Only** - Read-only access
- ❌ **No Status Changes** - Terminal state

**Next Possible States:**
- None (terminal state)

---

## 🎯 **List View Features**

### **Status-Based Display:**
- Documents displayed by **status** instead of draft/submitted
- Color-coded status indicators
- Quick status filtering buttons
- Bulk status update actions

### **Custom Formatting:**
- **Status Pills:** Color-coded status indicators
- **Scheduled DateTime:** Combined date and time display
- **Invoice Links:** Direct links to generated invoices
- **Auto-Invoice Indicator:** Visual checkbox for auto-generation

### **Workflow Actions:**
- **Individual Actions:** Status-specific buttons for each document
- **Bulk Actions:** Update multiple documents simultaneously
- **Quick Filters:** One-click status filtering
- **Workflow Diagram:** Visual representation of the process

---

## 🔧 **Technical Implementation**

### **List View Configuration:**
```javascript
// Status-based filtering instead of docstatus
filters: [
    ["status", "!=", ""]
],

// Custom status indicators
get_indicator: function(doc) {
    const status_colors = {
        "Scheduled": ["orange", "Scheduled"],
        "Aired": ["green", "Aired"], 
        "Missed": ["red", "Missed"],
        "Cancelled": ["grey", "Cancelled"]
    };
    return status_colors[doc.status] || ["grey", "Unknown"];
}
```

### **Action Handlers:**
```javascript
// Status-specific actions
const actions = {
    "Scheduled": mark_as_aired,
    "Aired": generate_invoice,
    "Missed": reschedule_advertisement
};
```

### **Bulk Operations:**
```javascript
// Bulk status updates
function bulk_status_update(listview, new_status) {
    const selected = listview.get_checked_items();
    // Update multiple documents simultaneously
}
```

---

## 📈 **Business Logic Integration**

### **Automatic Status Transitions:**

1. **Scheduled → Aired:**
   - Triggered by `log_broadcast()` method
   - Creates broadcast log entry
   - Updates status automatically

2. **Aired → Invoice Generated:**
   - Triggered when `auto_generate_invoice` is enabled
   - Creates sales invoice automatically
   - Links invoice to advertisement

3. **Missed → Scheduled:**
   - Manual reschedule action
   - Updates scheduled date/time
   - Resets status to Scheduled

### **Revenue Protection Logic:**
```python
# Auto-generate invoice when status changes to Aired
if self.auto_generate_invoice and self.status == "Aired":
    self.create_sales_invoice()

# Update status when broadcast is logged
if self.status == "Scheduled":
    self.status = "Aired"
```

---

## 🎨 **User Interface Enhancements**

### **Visual Workflow Diagram:**
```
[Scheduled] → [Aired] → [Invoice Generated]
     ↓
[Missed] → [Rescheduled]
     ↓
[Cancelled]
```

### **Status Filter Buttons:**
- **Scheduled** - Show pending broadcasts
- **Aired** - Show completed broadcasts
- **Missed** - Show failed broadcasts
- **Cancelled** - Show cancelled broadcasts
- **All** - Show all documents

### **Quick Actions Menu:**
- Mark Selected as Aired
- Mark Selected as Missed
- Cancel Selected
- Generate Invoices for Selected

---

## 🔍 **Monitoring & Analytics**

### **Key Metrics Tracking:**
- **Success Rate:** Aired vs Scheduled ratio
- **Revenue Impact:** Missed advertisement costs
- **Invoice Generation:** Automated vs manual
- **Presenter Performance:** Success rates by presenter

### **Dashboard Widgets:**
- Status distribution pie chart
- Revenue tracking over time
- Missed advertisement alerts
- Upcoming broadcasts timeline

---

## 🚀 **Best Practices**

### **For Operators:**
1. **Regular Monitoring:** Check list view frequently for status updates
2. **Quick Actions:** Use bulk operations for efficiency
3. **Status Filters:** Use filters to focus on specific stages
4. **Workflow Awareness:** Understand the complete flow

### **For Managers:**
1. **Performance Tracking:** Monitor success/failure rates
2. **Revenue Protection:** Address missed advertisements quickly
3. **Process Optimization:** Analyze workflow bottlenecks
4. **Team Training:** Ensure staff understand the workflow

### **For System Administrators:**
1. **Automation Setup:** Configure auto-invoice generation
2. **Notification Management:** Set up appropriate alerts
3. **Permission Management:** Control access to status changes
4. **Data Integrity:** Regular workflow audits

---

## 📋 **Implementation Checklist**

### ✅ **Completed Features:**
- [x] Status-based list view
- [x] Color-coded status indicators
- [x] Individual status actions
- [x] Bulk status operations
- [x] Quick status filters
- [x] Workflow diagram display
- [x] Custom formatters
- [x] Action buttons

### 🔄 **Next Steps:**
- [ ] Add workflow state validation
- [ ] Implement status change logging
- [ ] Create workflow analytics dashboard
- [ ] Add automated status transitions
- [ ] Implement notification triggers
- [ ] Create workflow reports

**The Advertisement Broadcast workflow is now fully implemented with comprehensive status management and user-friendly list view functionality!**
