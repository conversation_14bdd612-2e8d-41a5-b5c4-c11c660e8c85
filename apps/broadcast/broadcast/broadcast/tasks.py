import frappe
from frappe.utils import now_datetime, get_datetime, add_to_date


def mark_missed_advertisements():
    """Mark advertisements as missed if not broadcasted within threshold"""

    # Get threshold from settings
    threshold_hours = (
        frappe.db.get_single_value(
            "Broadcasting Settings", "auto_mark_missed_after_hours"
        )
        or 2
    )

    # Find advertisements that should be marked as missed
    threshold_time = add_to_date(now_datetime(), hours=-threshold_hours)

    missed_ads = frappe.db.sql(
        """
        SELECT name 
        FROM `tabAdvertisement Broadcast`
        WHERE status = 'Scheduled'
        AND CONCAT(scheduled_date, ' ', scheduled_time) < %s
        AND docstatus = 1
    """,
        (threshold_time,),
    )

    for ad_name in missed_ads:
        try:
            ad_doc = frappe.get_doc("Advertisement Broadcast", ad_name[0])
            ad_doc.status = "Missed"
            ad_doc.save(ignore_permissions=True)

            # Send missed advertisement alert
            send_missed_ad_alert(ad_doc)

        except Exception as e:
            frappe.log_error(
                message=str(e), title=f"Error marking ad as missed: {ad_name[0]}"
            )


def send_missed_ad_alert(ad_doc):
    """Send alert for missed advertisement"""

    recipients = [ad_doc.presenter]

    # Add broadcasting managers
    managers = frappe.get_all(
        "User", filters={"role_profile_name": "Broadcasting Manager"}, fields=["email"]
    )
    recipients.extend([m.email for m in managers])

    frappe.sendmail(
        recipients=recipients,
        subject=f"URGENT: Advertisement Missed - {ad_doc.advertisement_title}",
        message=f"""
        URGENT: Advertisement has been automatically marked as MISSED
        
        Advertisement: {ad_doc.advertisement_title}
        Customer: {ad_doc.customer}
        Scheduled: {ad_doc.scheduled_date} at {ad_doc.scheduled_time}
        Presenter: {ad_doc.presenter}
        Potential Revenue Loss: {frappe.utils.fmt_money(ad_doc.total_amount)}
        
        Immediate investigation required!
        """,
        reference_doctype="Advertisement Broadcast",
        reference_name=ad_doc.name,
    )
