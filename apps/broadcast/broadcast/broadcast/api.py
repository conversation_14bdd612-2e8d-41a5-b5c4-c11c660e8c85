import frappe
from frappe import _
from frappe.utils import now_datetime, get_datetime, cstr
import json


# ⭐ FRAPPE BEST PRACTICE: Simple @frappe.whitelist() decorator
@frappe.whitelist()
def log_detected_broadcast(
    advertisement_id, detected_datetime, duration, confidence, notes=None
):
    """
    API method to log detected broadcast

    Called via:
    - HTTP: POST /api/method/broadcast.broadcast.api.log_detected_broadcast
    - Frappe: frappe.call('broadcast.broadcast.api.log_detected_broadcast', {...})
    - Hooks: frappe.get_attr('broadcast.broadcast.api.log_detected_broadcast')
    """
    try:
        # Validate inputs using frappe's built-in validation
        if not advertisement_id:
            frappe.throw(_("Advertisement ID is required"))

        if not frappe.db.exists("Advertisement Broadcast", advertisement_id):
            frappe.throw(
                _("Advertisement Broadcast {0} not found").format(advertisement_id)
            )

        # Get document using frappe's ORM
        ad_broadcast = frappe.get_doc("Advertisement Broadcast", advertisement_id)

        # Validate permissions using frappe's permission system
        ad_broadcast.check_permission("write")

        # Validate confidence threshold
        min_confidence = (
            frappe.db.get_single_value(
                "Broadcasting Settings", "min_detection_confidence"
            )
            or 80
        )
        confidence = frappe.utils.flt(confidence)

        if confidence < min_confidence:
            return {
                "status": "warning",
                "message": _("Detection confidence {0}% below threshold {1}%").format(
                    confidence, min_confidence
                ),
                "requires_manual_verification": True,
            }

        # Use document method to log broadcast
        ad_broadcast.log_broadcast(
            actual_datetime=detected_datetime,
            actual_duration=frappe.utils.cint(duration),
            logged_by=frappe.session.user,
            broadcast_type="Auto Detection",
            confidence=confidence,
            notes=notes or _("Automatically detected broadcast"),
        )

        return {
            "status": "success",
            "message": _("Broadcast logged successfully"),
            "advertisement_broadcast": ad_broadcast.name,
            "invoice_generated": bool(ad_broadcast.sales_invoice),
        }

    except frappe.ValidationError as e:
        frappe.local.response["http_status_code"] = 400
        return {"status": "error", "message": str(e)}

    except frappe.PermissionError as e:
        frappe.local.response["http_status_code"] = 403
        return {"status": "error", "message": _("Permission denied")}

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Log Detected Broadcast Error"
        )
        frappe.local.response["http_status_code"] = 500
        return {"status": "error", "message": _("Internal server error")}


@frappe.whitelist()
def get_scheduled_broadcasts(hours_ahead=24):
    """
    Get scheduled broadcasts for specified hours ahead

    Called via:
    - HTTP: GET /api/method/broadcast.broadcast.api.get_scheduled_broadcasts
    - Frappe: frappe.call('broadcast.broadcast.api.get_scheduled_broadcasts')
    """
    try:
        hours_ahead = frappe.utils.cint(hours_ahead) or 24

        # Use frappe's date utilities
        from frappe.utils import add_to_date

        start_time = now_datetime()
        end_time = add_to_date(start_time, hours=hours_ahead)

        # Use frappe.db for database queries with proper escaping
        broadcasts = frappe.db.sql(
            """
            SELECT 
                name, 
                advertisement_title, 
                customer, 
                scheduled_date, 
                scheduled_time, 
                duration_seconds, 
                presenter,
                priority,
                total_amount
            FROM `tabAdvertisement Broadcast`
            WHERE status = 'Scheduled'
            AND docstatus = 1
            AND TIMESTAMP(scheduled_date, scheduled_time) BETWEEN %s AND %s
            ORDER BY scheduled_date, scheduled_time
        """,
            (start_time, end_time),
            as_dict=True,
        )

        # Check read permissions for each record
        filtered_broadcasts = []
        for broadcast in broadcasts:
            if frappe.has_permission("Advertisement Broadcast", "read", broadcast.name):
                filtered_broadcasts.append(broadcast)

        return {
            "status": "success",
            "broadcasts": filtered_broadcasts,
            "total_count": len(filtered_broadcasts),
        }

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Get Scheduled Broadcasts Error"
        )
        return {"status": "error", "message": _("Failed to fetch scheduled broadcasts")}


@frappe.whitelist()
def manual_broadcast_log(
    advertisement_id, actual_datetime, actual_duration, notes=None
):
    """
    Manual broadcast logging for presenters

    Called via:
    - HTTP: POST /api/method/broadcast.broadcast.api.manual_broadcast_log
    - JavaScript: frappe.call('broadcast.broadcast.api.manual_broadcast_log', {...})
    """
    try:
        if not advertisement_id:
            frappe.throw(_("Advertisement ID is required"))

        ad_broadcast = frappe.get_doc("Advertisement Broadcast", advertisement_id)
        ad_broadcast.check_permission("write")

        # Log the broadcast using document method
        ad_broadcast.log_broadcast(
            actual_datetime=actual_datetime,
            actual_duration=frappe.utils.cint(actual_duration),
            logged_by=frappe.session.user,
            broadcast_type="Manual Entry",
            notes=notes or _("Manually logged broadcast"),
        )

        return {
            "status": "success",
            "message": _("Broadcast logged manually"),
            "advertisement_broadcast": ad_broadcast.name,
        }

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Manual Broadcast Log Error"
        )
        frappe.local.response["http_status_code"] = 400
        return {"status": "error", "message": str(e)}


@frappe.whitelist()
def sync_detection_system():
    """
    Sync with external detection system - called by scheduler
    This method can be called from hooks.py scheduler_events
    """
    try:
        # Get all active advertisements for next 4 hours
        upcoming_ads = get_scheduled_broadcasts(hours_ahead=4)

        if upcoming_ads.get("status") == "success":
            broadcasts = upcoming_ads.get("broadcasts", [])

            # Log sync activity
            frappe.logger().info(
                f"Synced {len(broadcasts)} advertisements with detection system"
            )

            # You could call external system here if needed
            # But keeping it Frappe-only as requested

            return {"status": "success", "synced_count": len(broadcasts)}

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Detection System Sync Error"
        )
        return {"status": "error", "message": str(e)}


@frappe.whitelist()
def notify_detection_system(doc, method):
    """
    Called when Advertisement Broadcast is submitted
    This is called from hooks.py doc_events
    """
    try:
        if doc.doctype == "Advertisement Broadcast" and method == "on_submit":
            # Notify that new ad is scheduled
            frappe.logger().info(f"New advertisement scheduled: {doc.name}")

            # Create a background job using frappe.enqueue
            frappe.enqueue(
                method="broadcast.broadcast.api.process_new_advertisement",
                queue="short",
                advertisement_id=doc.name,
                is_async=True,
            )

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Detection System Notification Error"
        )


def process_new_advertisement(advertisement_id):
    """
    Background job to process new advertisement
    Called asynchronously via frappe.enqueue
    """
    try:
        ad_doc = frappe.get_doc("Advertisement Broadcast", advertisement_id)

        # Process the advertisement (e.g., prepare for detection)
        frappe.logger().info(f"Processing new advertisement: {advertisement_id}")

        # Update some status or send notification
        # This runs in background without blocking the UI

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Process New Advertisement Error"
        )


@frappe.whitelist()
def cancel_detection_monitoring(doc, method):
    """
    Called when Advertisement Broadcast is cancelled
    This is called from hooks.py doc_events
    """
    try:
        if doc.doctype == "Advertisement Broadcast" and method == "on_cancel":
            # Cancel any monitoring for this advertisement
            frappe.logger().info(f"Advertisement cancelled: {doc.name}")

            # You could cancel external monitoring here if needed
            # But keeping it Frappe-only as requested

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Cancel Detection Monitoring Error"
        )


@frappe.whitelist()
def send_scheduling_notification(doc, method):
    """
    DEPRECATED: Notification removed for better UX
    This is called from hooks.py doc_events but does nothing now
    """
    try:
        if doc.doctype == "Advertisement Broadcast" and method == "after_insert":
            # Just log the event, no notifications sent
            frappe.logger().info(f"New advertisement scheduled: {doc.name}")

            # Notifications removed to improve user experience
            # Users can check status via dashboard and reports

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(), title="Send Scheduling Notification Error"
        )
