# Copyright (c) 2025, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import now_datetime, get_datetime, flt, add_to_date
from datetime import datetime, timedelta
import json


class AdvertisementBroadcast(Document):
	def validate(self):
		self.calculate_total_amount()
		self.validate_schedule()

	def before_save(self):
		self.calculate_variance_for_logs()

	def on_submit(self):
		if self.auto_generate_invoice and self.status == "Aired":
			self.create_sales_invoice()

	def after_insert(self):
		# Removed automatic notifications for better UX
		# Only schedule essential reminders
		self.schedule_reminder_notifications()

	def calculate_total_amount(self):
		"""Calculate total amount based on duration and rate"""
		if self.duration_seconds and self.rate_per_second:
			self.total_amount = flt(self.duration_seconds) * flt(self.rate_per_second)

	def validate_schedule(self):
		"""Validate scheduling constraints"""
		scheduled_datetime = get_datetime(
			f"{self.scheduled_date} {self.scheduled_time}"
		)
		if scheduled_datetime < now_datetime():
			frappe.throw(_("Cannot schedule advertisement in the past"))

	def calculate_variance_for_logs(self):
		"""Calculate time variance for broadcast logs"""
		if not self.broadcast_logs:
			return

		scheduled_datetime = get_datetime(
			f"{self.scheduled_date} {self.scheduled_time}"
		)

		for log in self.broadcast_logs:
			if log.actual_broadcast_datetime:
				actual_datetime = get_datetime(log.actual_broadcast_datetime)
				variance = (actual_datetime - scheduled_datetime).total_seconds()
				log.variance_seconds = int(variance)

	def create_sales_invoice(self):
		"""Auto-generate sales invoice upon successful broadcast"""
		if self.sales_invoice:
			return

		# Calculate billable amount based on actual broadcasts
		billable_amount = 0
		for log in self.broadcast_logs:
			if log.actual_duration:
				billable_amount += flt(log.actual_duration) * flt(self.rate_per_second)

		if billable_amount == 0:
			billable_amount = self.total_amount

		invoice = frappe.get_doc(
			{
				"doctype": "Sales Invoice",
				"customer": self.customer,
				"posting_date": frappe.utils.today(),
				"items": [
					{
						"item_code": "Advertisement Broadcast Service",
						"description": f"Advertisement: {self.advertisement_title}",
						"qty": 1,
						"rate": billable_amount,
						"amount": billable_amount,
					}
				],
				"advertisement_broadcast": self.name,
			}
		)

		invoice.insert()
		invoice.submit()

		self.sales_invoice = invoice.name
		self.save()

		# Invoice notification removed for better UX
		# Invoice will be visible in the document

	def send_scheduling_notification(self):
		"""DEPRECATED: Notification removed for better UX"""
		# This method is kept for backward compatibility but does nothing
		# Notifications were causing poor user experience
		self.notification_sent = 0  # Mark as not sent since we're not sending

	def schedule_reminder_notifications(self):
		"""Schedule reminder notifications"""
		scheduled_datetime = get_datetime(
			f"{self.scheduled_date} {self.scheduled_time}"
		)

		# Schedule 1 hour before reminder
		reminder_time = scheduled_datetime - timedelta(hours=1)
		if reminder_time > now_datetime():
			frappe.enqueue_doc(
				"Advertisement Broadcast",
				self.name,
				"send_reminder_notification",
				queue="short",
				at_time=reminder_time,
			)

	def send_reminder_notification(self):
		"""Send reminder notification before broadcast"""
		frappe.sendmail(
			recipients=[self.presenter],
			subject=f"REMINDER: Advertisement in 1 hour - {self.advertisement_title}",
			message=f"""
			REMINDER: You have an advertisement broadcast in 1 hour!
			
			Advertisement: {self.advertisement_title}
			Customer: {self.customer}
			Scheduled: {self.scheduled_date} at {self.scheduled_time}
			Duration: {self.duration_seconds} seconds
			
			Please prepare for broadcast to avoid missed revenue.
			""",
			reference_doctype="Advertisement Broadcast",
			reference_name=self.name,
		)

	def log_broadcast(
		self,
		actual_datetime,
		actual_duration,
		logged_by,
		broadcast_type="Manual Entry",
		confidence=None,
		notes=None,
	):
		"""Log a broadcast occurrence"""
		log_entry = {
			"actual_broadcast_datetime": actual_datetime,
			"actual_duration": actual_duration,
			"broadcast_type": broadcast_type,
			"logged_by": logged_by,
			"notes": notes or "",
		}

		if confidence:
				log_entry["detection_confidence"] = confidence

		self.append("broadcast_logs", log_entry)

		# Update status based on logs
		if self.status == "Scheduled":
			self.status = "Aired"

		self.save()

		# Broadcast confirmation notification removed for better UX
		# Status change is sufficient indication

		# Auto-generate invoice if enabled
		if self.auto_generate_invoice:
			self.create_sales_invoice()

	def send_broadcast_confirmation(self):
		"""DEPRECATED: Confirmation notification removed for better UX"""
		# This method is kept for backward compatibility but does nothing
		# Status updates and dashboard provide sufficient feedback
		pass

	def send_invoice_notification(self, invoice_name):
		"""DEPRECATED: Invoice notification removed for better UX"""
		# This method is kept for backward compatibility but does nothing
		# Invoice will be linked and visible in the document
		pass
