frappe.listview_settings['Advertisement Broadcast'] = {
    // Display documents by status instead of draft/submitted
    add_fields: ["status", "scheduled_date", "scheduled_time", "customer", "presenter", "total_amount", "auto_generate_invoice", "sales_invoice"],
    
    // Status-based filtering instead of docstatus
    filters: [
        ["status", "!=", ""]
    ],
    
    // Hide standard draft/submitted filters
    hide_name_column: false,
    
    // Custom list view formatting
    get_indicator: function(doc) {
        const status_colors = {
            "Scheduled": ["orange", "Scheduled"],
            "Aired": ["green", "Aired"],
            "Missed": ["red", "Missed"],
            "Cancelled": ["grey", "Cancelled"]
        };
        
        if (status_colors[doc.status]) {
            return status_colors[doc.status];
        }
        return ["grey", "Unknown"];
    },
    
    // Custom column formatting
    formatters: {
        status: function(value, field, doc) {
            const status_colors = {
                "Scheduled": "orange",
                "Aired": "green", 
                "Missed": "red",
                "Cancelled": "grey"
            };
            
            const color = status_colors[value] || "grey";
            return `<span class="indicator-pill ${color}">${value}</span>`;
        },
        
        scheduled_date: function(value, field, doc) {
            if (value) {
                const date = frappe.datetime.str_to_user(value);
                const time = doc.scheduled_time || "";
                return `${date} ${time}`;
            }
            return "";
        },
        
        total_amount: function(value, field, doc) {
            if (value) {
                return format_currency(value, frappe.defaults.get_default("currency"));
            }
            return "";
        },
        
        auto_generate_invoice: function(value, field, doc) {
            if (value) {
                return '<span class="text-success"><i class="fa fa-check"></i></span>';
            }
            return '<span class="text-muted"><i class="fa fa-times"></i></span>';
        },
        
        sales_invoice: function(value, field, doc) {
            if (value) {
                return `<a href="/app/sales-invoice/${value}" target="_blank">${value}</a>`;
            }
            return '<span class="text-muted">Not Generated</span>';
        }
    },
    
    // Status-based action buttons
    button: {
        show: function(doc) {
            return doc.status !== "Cancelled";
        },
        get_label: function(doc) {
            const next_actions = {
                "Scheduled": "Mark as Aired",
                "Aired": "Generate Invoice",
                "Missed": "Reschedule"
            };
            return next_actions[doc.status] || "Action";
        },
        get_description: function(doc) {
            const descriptions = {
                "Scheduled": "Mark this advertisement as successfully aired",
                "Aired": "Generate sales invoice for this broadcast",
                "Missed": "Reschedule this missed advertisement"
            };
            return descriptions[doc.status] || "";
        },
        action: function(doc) {
            handle_status_action(doc);
        }
    },
    
    // Custom bulk actions for status management
    onload: function(listview) {
        // Add custom bulk actions
        listview.page.add_menu_item(__("Mark Selected as Aired"), function() {
            bulk_status_update(listview, "Aired");
        });
        
        listview.page.add_menu_item(__("Mark Selected as Missed"), function() {
            bulk_status_update(listview, "Missed");
        });
        
        listview.page.add_menu_item(__("Cancel Selected"), function() {
            bulk_status_update(listview, "Cancelled");
        });
        
        // Add status filter buttons
        add_status_filter_buttons(listview);
        
        // Add workflow stage indicators
        add_workflow_indicators(listview);
    },
    
    // Custom refresh to maintain status-based view
    refresh: function(listview) {
        // Ensure we're showing status-based view
        listview.filter_area.clear();
        listview.filter_area.add([
            ["Advertisement Broadcast", "status", "!=", ""]
        ]);
    }
};

// Handle individual status actions
function handle_status_action(doc) {
    const actions = {
        "Scheduled": function() {
            mark_as_aired(doc);
        },
        "Aired": function() {
            generate_invoice(doc);
        },
        "Missed": function() {
            reschedule_advertisement(doc);
        }
    };
    
    if (actions[doc.status]) {
        actions[doc.status]();
    }
}

// Mark advertisement as aired
function mark_as_aired(doc) {
    frappe.confirm(
        __('Mark "{0}" as Aired?', [doc.name]),
        function() {
            frappe.call({
                method: 'broadcast.broadcast.api.manual_broadcast_log',
                args: {
                    advertisement_id: doc.name,
                    actual_datetime: frappe.datetime.now_datetime(),
                    actual_duration: 30, // Default duration
                    notes: 'Marked as aired from list view'
                },
                freeze: true,
                freeze_message: __('Updating status...'),
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        frappe.show_alert({
                            message: __('Advertisement marked as aired'),
                            indicator: 'green'
                        });
                        cur_list.refresh();
                    } else {
                        frappe.show_alert({
                            message: __('Failed to update status'),
                            indicator: 'red'
                        });
                    }
                }
            });
        }
    );
}

// Generate invoice for aired advertisement
function generate_invoice(doc) {
    if (doc.sales_invoice) {
        frappe.show_alert({
            message: __('Invoice already generated: {0}', [doc.sales_invoice]),
            indicator: 'blue'
        });
        return;
    }
    
    frappe.confirm(
        __('Generate Sales Invoice for "{0}"?', [doc.name]),
        function() {
            frappe.call({
                method: 'create_sales_invoice',
                doc: doc,
                freeze: true,
                freeze_message: __('Generating invoice...'),
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Sales invoice generated successfully'),
                            indicator: 'green'
                        });
                        cur_list.refresh();
                    } else {
                        frappe.show_alert({
                            message: __('Failed to generate invoice'),
                            indicator: 'red'
                        });
                    }
                }
            });
        }
    );
}

// Reschedule missed advertisement
function reschedule_advertisement(doc) {
    const dialog = new frappe.ui.Dialog({
        title: __('Reschedule Advertisement'),
        fields: [
            {
                fieldtype: 'Date',
                fieldname: 'new_date',
                label: __('New Scheduled Date'),
                reqd: 1,
                default: frappe.datetime.add_days(frappe.datetime.nowdate(), 1)
            },
            {
                fieldtype: 'Time',
                fieldname: 'new_time',
                label: __('New Scheduled Time'),
                reqd: 1,
                default: doc.scheduled_time
            },
            {
                fieldtype: 'Small Text',
                fieldname: 'reschedule_reason',
                label: __('Reason for Rescheduling'),
                reqd: 1
            }
        ],
        primary_action_label: __('Reschedule'),
        primary_action: function(values) {
            frappe.call({
                method: 'frappe.client.set_value',
                args: {
                    doctype: 'Advertisement Broadcast',
                    name: doc.name,
                    fieldname: {
                        'scheduled_date': values.new_date,
                        'scheduled_time': values.new_time,
                        'status': 'Scheduled'
                    }
                },
                freeze: true,
                freeze_message: __('Rescheduling...'),
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Advertisement rescheduled successfully'),
                            indicator: 'green'
                        });
                        dialog.hide();
                        cur_list.refresh();
                    }
                }
            });
        }
    });
    
    dialog.show();
}

// Bulk status update
function bulk_status_update(listview, new_status) {
    const selected = listview.get_checked_items();
    
    if (selected.length === 0) {
        frappe.show_alert({
            message: __('Please select items to update'),
            indicator: 'orange'
        });
        return;
    }
    
    frappe.confirm(
        __('Update status of {0} selected items to "{1}"?', [selected.length, new_status]),
        function() {
            frappe.call({
                method: 'frappe.client.set_value',
                args: {
                    doctype: 'Advertisement Broadcast',
                    name: selected.map(item => item.name),
                    fieldname: 'status',
                    value: new_status
                },
                freeze: true,
                freeze_message: __('Updating status...'),
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Status updated for {0} items', [selected.length]),
                            indicator: 'green'
                        });
                        listview.refresh();
                    }
                }
            });
        }
    );
}

// Add status filter buttons
function add_status_filter_buttons(listview) {
    const statuses = ['Scheduled', 'Aired', 'Missed', 'Cancelled'];
    
    statuses.forEach(status => {
        listview.page.add_inner_button(__(status), function() {
            listview.filter_area.clear();
            listview.filter_area.add([
                ["Advertisement Broadcast", "status", "=", status]
            ]);
            listview.refresh();
        });
    });
    
    // Add "All" button
    listview.page.add_inner_button(__("All"), function() {
        listview.filter_area.clear();
        listview.refresh();
    });
}

// Add workflow stage indicators
function add_workflow_indicators(listview) {
    // Add workflow flow diagram to the page
    const workflow_html = `
        <div class="workflow-diagram" style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <h6>Advertisement Broadcast Workflow:</h6>
            <div class="workflow-stages">
                <span class="badge badge-warning">Scheduled</span>
                <i class="fa fa-arrow-right"></i>
                <span class="badge badge-success">Aired</span>
                <i class="fa fa-arrow-right"></i>
                <span class="badge badge-info">Invoice Generated</span>
                <br><br>
                <span class="badge badge-danger">Missed</span>
                <i class="fa fa-arrow-right"></i>
                <span class="badge badge-warning">Rescheduled</span>
                <br><br>
                <span class="badge badge-secondary">Cancelled</span>
            </div>
        </div>
    `;
    
    if (!listview.page.wrapper.find('.workflow-diagram').length) {
        listview.page.wrapper.find('.layout-main-section').prepend(workflow_html);
    }
}
