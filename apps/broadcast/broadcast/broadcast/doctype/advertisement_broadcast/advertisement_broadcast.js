frappe.ui.form.on('Advertisement Broadcast', {
    refresh: function(frm) {
        // Add custom button that calls API method
        if (frm.doc.status === 'Scheduled') {
            frm.add_custom_button(__('Log Broadcast'), function() {
                // ⭐ PROPER FRAPPE WAY to call API method
                frappe.call({
                    method: 'broadcast.broadcast.api.manual_broadcast_log',
                    args: {
                        advertisement_id: frm.doc.name,
                        actual_datetime: frappe.datetime.now_datetime(),
                        actual_duration: frm.doc.duration_seconds,
                        notes: 'Logged via custom button'
                    },
                    callback: function(r) {
                        if (r.message && r.message.status === 'success') {
                            frappe.show_alert({
                                message: __('Broadcast logged successfully'),
                                indicator: 'green'
                            });
                            frm.reload_doc();
                        } else {
                            frappe.show_alert({
                                message: r.message ? r.message.message : __('Error logging broadcast'),
                                indicator: 'red'
                            });
                        }
                    }
                });
            });
        }
        
        // Button to get scheduled broadcasts
        frm.add_custom_button(__('View Scheduled'), function() {
            frappe.call({
                method: 'broadcast.broadcast.api.get_scheduled_broadcasts',
                args: {
                    hours_ahead: 24
                },
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        // Show scheduled broadcasts in a dialog
                        show_scheduled_broadcasts_dialog(r.message.broadcasts);
                    }
                }
            });
        }, __('Actions'));
    }
});

function show_scheduled_broadcasts_dialog(broadcasts) {
    let dialog = new frappe.ui.Dialog({
        title: __('Scheduled Broadcasts'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'broadcasts_html'
            }
        ]
    });
    
    let html = '<table class="table table-bordered">';
    html += '<tr><th>Title</th><th>Customer</th><th>Scheduled</th><th>Presenter</th></tr>';
    
    broadcasts.forEach(function(broadcast) {
        html += `<tr>
            <td>${broadcast.advertisement_title}</td>
            <td>${broadcast.customer}</td>
            <td>${broadcast.scheduled_date} ${broadcast.scheduled_time}</td>
            <td>${broadcast.presenter}</td>
        </tr>`;
    });
    
    html += '</table>';
    
    dialog.fields_dict.broadcasts_html.$wrapper.html(html);
    dialog.show();
}