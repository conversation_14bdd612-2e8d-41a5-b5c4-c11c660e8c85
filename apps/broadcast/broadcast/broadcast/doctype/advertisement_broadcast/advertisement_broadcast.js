frappe.ui.form.on('Advertisement Broadcast', {
    refresh: function(frm) {
        // Add custom button that calls API method
        if (frm.doc.status === 'Scheduled') {
            frm.add_custom_button(__('Log Broadcast'), function() {
                // ⭐ PROPER FRAPPE WAY to call API method
                frappe.call({
                    method: 'broadcast.broadcast.api.manual_broadcast_log',
                    args: {
                        advertisement_id: frm.doc.name,
                        actual_datetime: frappe.datetime.now_datetime(),
                        actual_duration: frm.doc.duration_seconds || 30,
                        notes: 'Logged via custom button'
                    },
                    freeze: true,
                    freeze_message: __('Logging broadcast...'),
                    callback: function(r) {
                        if (r.message && r.message.status === 'success') {
                            frappe.show_alert({
                                message: __('Broadcast logged successfully'),
                                indicator: 'green'
                            });
                            frm.reload_doc();
                        } else {
                            frappe.show_alert({
                                message: r.message ? r.message.message : __('Error logging broadcast'),
                                indicator: 'red'
                            });
                        }
                    },
                    error: function(r) {
                        frappe.show_alert({
                            message: __('Failed to log broadcast. Please try again.'),
                            indicator: 'red'
                        });
                        console.error('Broadcast logging error:', r);
                    }
                });
            }, __('Actions'));
        }

        // Button to get scheduled broadcasts
        frm.add_custom_button(__('View Scheduled'), function() {
            frappe.call({
                method: 'broadcast.broadcast.api.get_scheduled_broadcasts',
                args: {
                    hours_ahead: 24
                },
                freeze: true,
                freeze_message: __('Loading scheduled broadcasts...'),
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        // Show scheduled broadcasts in a dialog
                        show_scheduled_broadcasts_dialog(r.message.broadcasts);
                    } else {
                        frappe.show_alert({
                            message: __('Failed to load scheduled broadcasts'),
                            indicator: 'orange'
                        });
                    }
                },
                error: function(r) {
                    frappe.show_alert({
                        message: __('Error loading scheduled broadcasts'),
                        indicator: 'red'
                    });
                    console.error('Scheduled broadcasts error:', r);
                }
            });
        }, __('Actions'));
    },

    // Handle auto_generate_invoice checkbox changes
    auto_generate_invoice: function(frm) {
        if (frm.doc.auto_generate_invoice && frm.doc.status === 'Aired' && !frm.doc.sales_invoice) {
            frappe.confirm(
                __('Do you want to generate a sales invoice now?'),
                function() {
                    // Generate invoice immediately
                    frappe.call({
                        method: 'create_sales_invoice',
                        doc: frm.doc,
                        freeze: true,
                        freeze_message: __('Generating invoice...'),
                        callback: function(r) {
                            if (!r.exc) {
                                frappe.show_alert({
                                    message: __('Sales invoice generated successfully'),
                                    indicator: 'green'
                                });
                                frm.reload_doc();
                            } else {
                                frappe.show_alert({
                                    message: __('Error generating invoice: ') + (r.exc || 'Unknown error'),
                                    indicator: 'red'
                                });
                            }
                        },
                        error: function(r) {
                            frappe.show_alert({
                                message: __('Failed to generate invoice. Please check if customer exists and try again.'),
                                indicator: 'red'
                            });
                            console.error('Invoice generation error:', r);
                        }
                    });
                }
            );
        }
    },

    // Handle status changes
    status: function(frm) {
        if (frm.doc.status === 'Aired' && frm.doc.auto_generate_invoice && !frm.doc.sales_invoice) {
            // Auto-generate invoice when status changes to Aired
            frappe.call({
                method: 'create_sales_invoice',
                doc: frm.doc,
                freeze: true,
                freeze_message: __('Auto-generating invoice...'),
                callback: function(r) {
                    if (!r.exc) {
                        frappe.show_alert({
                            message: __('Sales invoice auto-generated'),
                            indicator: 'green'
                        });
                        frm.reload_doc();
                    } else {
                        frappe.show_alert({
                            message: __('Error auto-generating invoice: ') + (r.exc || 'Unknown error'),
                            indicator: 'red'
                        });
                    }
                },
                error: function(r) {
                    frappe.show_alert({
                        message: __('Failed to auto-generate invoice. Please check if customer exists.'),
                        indicator: 'red'
                    });
                    console.error('Auto-invoice generation error:', r);
                }
            });
        }
    }
});

function show_scheduled_broadcasts_dialog(broadcasts) {
    let dialog = new frappe.ui.Dialog({
        title: __('Scheduled Broadcasts'),
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'broadcasts_html'
            }
        ]
    });
    
    let html = '<table class="table table-bordered">';
    html += '<tr><th>Title</th><th>Customer</th><th>Scheduled</th><th>Presenter</th></tr>';
    
    broadcasts.forEach(function(broadcast) {
        html += `<tr>
            <td>${broadcast.advertisement_title}</td>
            <td>${broadcast.customer}</td>
            <td>${broadcast.scheduled_date} ${broadcast.scheduled_time}</td>
            <td>${broadcast.presenter}</td>
        </tr>`;
    });
    
    html += '</table>';
    
    dialog.fields_dict.broadcasts_html.$wrapper.html(html);
    dialog.show();
}